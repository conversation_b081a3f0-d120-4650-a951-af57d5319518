* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Nav */
nav {
  height: 70px;
  /* Use transparent background to allow global gradient to show through */
  background: transparent;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1000;
  position: sticky;
  border-bottom: 2px solid var(--border-pink-dark);
}
nav svg {
  display: none;
}
.logo h2 {
  font-weight: 700;
  font-size: 2rem;
  color: var(--text-primary);
  cursor: pointer;
  margin: 0 0.5rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.logo h2::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  bottom: -5px;
  left: 0;
  background: linear-gradient(to right, var(--primary-pink), var(--accent-pink));
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

.logo h2:hover::before {
  transform: scaleX(1);
}
.nav-items {
  display: flex;
  justify-content: space-between;

}
.overview,
.account {
  display: flex;
}
.overview {
  margin-right: 4rem;
}
.nav-items h3 {
  display: none;
}
nav li {
  list-style: none;
  margin: 0 0.5rem;
}
nav a {
  text-decoration: none;
  color: var(--text-secondary);
  font-size: 18px;
}
nav a:hover {
  color: var(--primary-pink-dark);
}
nav a::after {
  content: "";
  display: block;
  height: 3px;
  background: var(--primary-pink);
  width: 0%;
  transition: all ease-in-out 300ms;
}
nav a:hover::after {
  width: 100%;
}
#check,
.menu {
  display: none;
}




@media (max-width: 750px) {
  .nav-items {
    z-index: 1000;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 250px;
    flex-direction: column;
    justify-content: space-evenly;
    background: var(--accent-pink);
    padding: 2rem;
    right: -250px;
    transition: all ease-in-out 500ms;
  }
  .overview,
  .account {
    flex-direction: column;
    width: auto;
  }
  .overview {
    margin: 0;
  }
  .nav-items h3 {
    display: inline-block;
    font-weight: 400;
    text-transform: uppercase;
    font-size: 13px;
    margin-bottom: 1rem;
  }
  nav svg {
    display: inline-block;
    cursor: pointer;
    vertical-align: top;
  }
  nav li {
    margin: 1rem 0;
  }
  nav a {
    display: inline-block;
  }
  nav a:hover {
    margin-left: 2px;
    transition: all ease-in-out 300ms;
  }
  .menu {
    display: inline-block;
    position: fixed;
    right: 2.5rem;
    z-index: 1001;
  }

  #check:checked ~ .nav-items {
    right: 0;
  }
}
