import { Component, OnIni<PERSON>, <PERSON><PERSON><PERSON>roy, ElementRef } from '@angular/core';
import { DarkModeService } from '../services/dark-mode.service';
import { Subscription } from 'rxjs';

declare var VANTA: any;

@Component({
  selector: 'app-first',
  templateUrl: './first.component.html',
  styleUrl: './first.component.css'
})
export class FirstComponent implements OnInit, OnDestroy {
  private vantaEffect: any;
  private subscription: Subscription = new Subscription();
  isDarkMode = false;

  private originalSettings = {
    speedLimit: 9.00,
    separation: 58.00,
    cohesion: 40.00
  };
  private hoverSettings = {
    speedLimit: 25.00,
    separation: 250.00, // Increase separation for scatter effect
    cohesion: 10.00      // Reduce cohesion so birds don't flock together
  };

  // Theme-specific Vanta.js colors
  private lightThemeColors = {
    backgroundColor: 0xfdf2f8, // Light pink background
    color1: 0xac0e31,         // Primary pink
    color2: 0x6700ff,         // Accent pink
    backgroundAlpha: 0.8
  };

  private darkThemeColors = {
    backgroundColor: 0x000000, // Pure black background
    color1: 0xF48FB1,         // Soft pink for dark mode
    color2: 0xFFB6C1,         // Light pink accent
    backgroundAlpha: 0.9
  };

  constructor(
    private elementRef: ElementRef,
    private darkModeService: DarkModeService
  ) {}

  ngOnInit() {
    // Subscribe to dark mode changes
    this.subscription.add(
      this.darkModeService.isDarkMode$.subscribe(isDark => {
        this.isDarkMode = isDark;
        this.updateVantaTheme();
      })
    );

    // Initialize Vanta.js with current theme
    this.initializeVanta();
    this.setupHoverEffects();
  }

  private initializeVanta() {
    const currentTheme = this.isDarkMode ? this.darkThemeColors : this.lightThemeColors;

    this.vantaEffect = VANTA.BIRDS({
      el: "#vanta-birds",
      mouseControls: true,
      touchControls: true,
      gyroControls: false,
      minHeight: 200.00,
      minWidth: 200.00,
      scale: 1.00,
      scaleMobile: 1.00,
      backgroundColor: currentTheme.backgroundColor,
      color1: currentTheme.color1,
      color2: currentTheme.color2,
      birdSize: 1.20,
      wingSpan: 32.00,
      speedLimit: this.originalSettings.speedLimit,
      separation: this.originalSettings.separation,
      alignment: 77.00,
      cohesion: this.originalSettings.cohesion,
      quantity: 4.00,
      backgroundAlpha: currentTheme.backgroundAlpha
    });
  }

  private updateVantaTheme() {
    if (this.vantaEffect) {
      const currentTheme = this.isDarkMode ? this.darkThemeColors : this.lightThemeColors;
      this.vantaEffect.setOptions({
        backgroundColor: currentTheme.backgroundColor,
        color1: currentTheme.color1,
        color2: currentTheme.color2,
        backgroundAlpha: currentTheme.backgroundAlpha
      });
    }
  }

  private setupHoverEffects() {
    const heroSection = this.elementRef.nativeElement.querySelector('#vanta-birds');

    if (heroSection) {
      heroSection.addEventListener('mouseenter', () => {
        this.onHoverStart();
      });

      heroSection.addEventListener('mouseleave', () => {
        this.onHoverEnd();
      });
    }
  }

  private onHoverStart() {
    if (this.vantaEffect) {
      this.vantaEffect.setOptions({
        speedLimit: this.hoverSettings.speedLimit,
        separation: this.hoverSettings.separation,
        cohesion: this.hoverSettings.cohesion
      });
    }
  }

  private onHoverEnd() {
    if (this.vantaEffect) {
      this.vantaEffect.setOptions({
        speedLimit: this.originalSettings.speedLimit,
        separation: this.originalSettings.separation,
        cohesion: this.originalSettings.cohesion
      });
    }
  }

  ngOnDestroy() {
    // Clean up subscriptions
    this.subscription.unsubscribe();

    // Clean up Vanta.js effect
    if (this.vantaEffect) {
      this.vantaEffect.destroy();
    }
  }
}
